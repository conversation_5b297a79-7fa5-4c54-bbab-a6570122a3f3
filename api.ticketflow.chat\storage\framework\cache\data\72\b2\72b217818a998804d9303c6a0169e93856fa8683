1753669702O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:19:"App\Models\Currency":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"currencies";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:1;s:6:"symbol";s:2:"R$";s:5:"title";s:3:"BRL";s:8:"position";s:6:"before";s:7:"default";i:1;s:6:"active";i:1;s:10:"created_at";N;s:10:"updated_at";N;s:10:"deleted_at";N;s:4:"rate";d:1;}s:11:" * original";a:10:{s:2:"id";i:1;s:6:"symbol";s:2:"R$";s:5:"title";s:3:"BRL";s:8:"position";s:6:"before";s:7:"default";i:1;s:6:"active";i:1;s:10:"created_at";N;s:10:"updated_at";N;s:10:"deleted_at";N;s:4:"rate";d:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}